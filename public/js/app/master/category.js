$("#datatable").DataTable({
    processing: true,
    serverSide: true,
    ajax: "/master-data/data-kategori-list",
    columns: [
        {data: "DT_RowIndex", name: "DT_RowIndex", orderable: false, searchable: false},
        {data: "category_code", name: "category_code"},
        {data: "category_name", name: "category_name"},
        {
            name: "category_type",
            data: "category_type", render: function (v) {
                const categoryMap = {
                    "EQUIPMENT": "Peralatan",
                    "LOGISTIC": "Logistik",
                    "INFRASTRUCTURE": "Infrastruktur",
                    "OTHER": "Lainnya"
                };
                return categoryMap[v] || "";
            },
            searchable: false
        },
        {
            name: "category_sub_type", data: "category_sub_type", render: function (v) {
                const categoryMap = {
                    "ALAT_KESEHATAN": "Alat Kesehatan",
                    "NON_ALAT_KESEHATAN": "Non Alat Kesehatan"
                };
                return categoryMap[v] || "";
            },
            searchable: false
        },
        {data: "employee_name", name: "employee_name", searchable: false},
        {data: "action", name: "action", orderable: false, searchable: false},
    ],
});

$("#btn-add").on("click", function () {
    $("#kode_kategori").val("");
    $("#nama_kategori").val("");
    $("#tipe_kategori").val("");
    $("#sub_tipe_kategori").val("");
    let newOption = new Option("", "", true, true);
    $("#kategori_pic").append(newOption).trigger("change");

    $("#modal-dialog").modal("show");
    update = false;
    changeActionForm("/master-data/data-kategori/store")
})

$("#datatable").on("click", ".btn-edit", function () {
    let route = $(this).data("route");
    update = true;
    changeActionForm(route);

    $.ajax({
        url: route,
        type: "GET",
        success: function (response) {

            $("#kode_kategori").val(response.data.category_code);
            $("#nama_kategori").val(response.data.category_name);
            $("#tipe_kategori").val(response.data.category_type);
            $("#sub_tipe_kategori").val(response.data.category_sub_type);
            $("#kategori_pic")
            .append(
                new Option(
                    `${response.data.employee_identification_number} - ${response.data.employee_name}`,
                    response.data.pic_category,
                    true,
                    true,
                ),
            )
            .trigger("change");

            $("#modal-dialog").modal("show");
            $("#formdata").attr("action",)
        },
        error: function (xhr) {
            errorMessage(xhr.responseJSON.message);
        },
    })
})

$(".kategori_pic").select2({
    ajax: {
        url: "/dropdown/employee",
        dataType: "json",
        delay: 250,
        data: function (params) {
            return {
                q: params.term,
                page: params.page || 1,
            };
        },
        processResults: function (data, params) {
            params.page = params.page || 1;

            return {
                results: $.map(data.data, function (item) {

                    return {
                        id: item.pic.id,
                        text:
                            item.pic.employee_identification_number +
                            " - " +
                            item.pic.employee_name,
                    };
                }),
                pagination: {
                    more: data.current_page < data.last_page,
                },
            };
        },
        cache: true,
    },

    placeholder: "Pilih kategori PIC",
    minimumInputLength: 0,
    allowClear: true,
    width: "100%",
    dropdownParent: $(".kategori_pic").parent(),
});

// Import Excel functionality
$("#btn-import-excel").on("click", function(e) {
    e.preventDefault();
    // Reset form
    $("#form-import-excel")[0].reset();
    $(".error-message").text("");
    $("#modal-import-excel").modal("show");
});

// Handle import submit
$("#btn-import-submit").on("click", function(e) {
    e.preventDefault();
    
    const fileInput = $("#excel_file")[0];
    const file = fileInput.files[0];
    
    // Validate file
    if (!file) {
        $("#error_excel_file").text("Silakan pilih file Excel terlebih dahulu.");
        return;
    }
    
    // Validate file type
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
        $("#error_excel_file").text("Format file tidak didukung. Gunakan file .xlsx atau .xls");
        return;
    }
    
    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
        $("#error_excel_file").text("Ukuran file terlalu besar. Maksimal 5MB.");
        return;
    }
    
    // Clear previous errors
    $(".error-message").text("");
    
    // Show loading state
    const originalText = $("#btn-import-submit").html();
    $("#btn-import-submit").html('<i class="fas fa-spinner fa-spin me-1"></i>Mengimport...').prop('disabled', true);
    
    // Create FormData
    const formData = new FormData();
    formData.append('excel_file', file);
    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
    
    // Submit form via AJAX
    $.ajax({
        url: '/master-data/data-kategori/import',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            // Reset loading state
            $("#btn-import-submit").html(originalText).prop('disabled', false);
            
            if (response.success) {
                $("#modal-import-excel").modal("hide");
                
                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: response.message || 'Data kategori berhasil diimport.',
                    showConfirmButton: false,
                    timer: 2000
                });
                
                // Reload datatable
                $("#datatable").DataTable().ajax.reload();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal!',
                    text: response.message || 'Terjadi kesalahan saat mengimport data.'
                });
            }
        },
        error: function(xhr) {
            // Reset loading state
            $("#btn-import-submit").html(originalText).prop('disabled', false);
            
            let errorMessage = 'Terjadi kesalahan saat mengimport data.';
            
            if (xhr.responseJSON) {
                if (xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                // Handle validation errors
                if (xhr.responseJSON.errors) {
                    const errors = xhr.responseJSON.errors;
                    if (errors.excel_file) {
                        $("#error_excel_file").text(errors.excel_file[0]);
                        return;
                    }
                }
            }
            
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: errorMessage
            });
        }
    });
});

// Reset form when modal is hidden
$("#modal-import-excel").on('hidden.bs.modal', function() {
    $("#form-import-excel")[0].reset();
    $(".error-message").text("");
    $("#btn-import-submit").html('<i class="fas fa-upload me-1"></i>Import').prop('disabled', false);
});
