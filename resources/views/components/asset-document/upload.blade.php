<a href="#modal-upload-doc" class="btn btn-primary" data-bs-toggle="modal"><i class="fas fa-upload me-1"></i>Upload
    Dokumen</a>

<div class="modal fade" id="modal-upload-doc">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Upload Dokumen</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <form action="{{ route('asset-management.asset-document.upload') }}" method="post" id="form-upload-doc"
                enctype="multipart/form-data">
                <div class="modal-body row">
                    @csrf
                    <div class="form-group col-md-6 mb-3">
                        <label class="form-label">Tipe</label>
                        <select name="tipe" id="tipe" class="form-select">
                            <option disabled selected>-- Pilih Tipe --</option>
                            <option value="checklist">BAST Checklist</option>
                            <option value="penempatan">BAST Penempatan</option>
                            <option value="mutasi">BAST Mutasi</option>
                            <option value="pembayaran">BAST Pembayaran</option>
                            <option value="rusak">BAST Aset Rusak</option>
                            <option value="penempatan_lama">BAST Penempatan Lama</option>
                        </select>

                        @error('tipe')
                            <span class="d-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-md-6 mb-3 d-none" id="document_code_field">
                        <label class="form-label">Kode Dokumen / No. Dokumen <small
                                class="text-secondary d-block">*Tekan enter untuk memunculkan asset.</small></label>
                        <input type="text" name="kode_dokumen" id="kode_dokumen" class="form-control text-uppercase">

                        @error('kode_dokumen')
                            <span class="d-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-md-12 mb-3 d-none" id="document_field">
                        <label class="form-label">Dokumen</label>
                        <input type="file" name="dokumen" id="dokumen" accept="application/pdf"
                            class="form-control">

                        @error('dokumen')
                            <span class="d-block text-danger">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="form-group col-md-12 mb-3 d-none" id="ruangan_field">
                        <label class="form-label">Ruangan</label>
                        <select name="ruangan_upload" id="ruangan_upload" class="form-select ruangan-upload">
                            <option disabled selected>-- Pilih Ruangan --</option>
                        </select>
                    </div>

                    <div class="form-group col-md-12">
                        <div class="d-flex justify-content-end mb-3">
                            <button type="button" class="btn btn-outline-primary d-none" id="btn-add-row-upload"><i
                                    class="fas fa-plus-circle me-1"></i>Tambah Baris</button>
                        </div>

                        <table class="table table-bordered table-striped w-100 d-none" id="table-upload">
                            <thead>
                                <tr>
                                    <th>Kode Asset</th>
                                    <th>Kode QR</th>
                                    <th>Kode Register / SN</th>
                                    <th class="text-center">Foto</th>
                                    <th class="text-center">#</th>
                                </tr>
                            </thead>
                            <tbody id="table-upload-doc"></tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" id="btn-close-upload" data-bs-dismiss="modal">Close</a>
                    <button type="submit" class="btn btn-primary" id="btn-upload-doc"><i
                            class="fas fa-upload me-1"></i>Upload Dokumen</button>
                </div>
            </form>
        </div>
    </div>
</div>
