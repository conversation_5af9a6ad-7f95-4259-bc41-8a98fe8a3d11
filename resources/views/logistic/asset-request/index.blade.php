@extends("layouts.app")

@push("style")
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
@endpush

@push("menu")
    @include("menu.logistic")
@endpush

@section("content")
    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
        </div>
        <div class="panel-body">
            <div class="d-flex justify-content-between mb-3">
                <select style="width: auto" name="target_ruangan" id="target_ruangan" class="form-select target_ruangan"></select>
                <a href="{{ route('logistic.asset-request.create') }}" class="btn btn-primary"><i class="fas fa-plus-circle me-1"></i>Buat Form Request Permintaan Barang</a>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped w-100" id="datatable">
                    <thead>
                    <tr>
                        <th>#</th>
                        <th>Tanggal</th>
                        <th>Nomor Dokumen</th>
                        <th>Ruangan</th>
                        <th>Bidang Permintaan</th>
                        <th>Catatan</th>
                        <th>Pembuat</th>
                        <th>Aksi</th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-dialog">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Detail Permintaan Barang</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div id="activityBody" class="modal-body"></div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Tutup</a>
                    <a href="javascript:;" class="btn btn-warning btn-export-excel" data-id=""><i class="fas fa-file-pdf me-1"></i> Print PDF</a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push("script")
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/js/app/logistic/app.js') }}"></script>
    <script src="{{ asset('/js/app/logistic/requestAsset.js') }}"></script>
@endpush
