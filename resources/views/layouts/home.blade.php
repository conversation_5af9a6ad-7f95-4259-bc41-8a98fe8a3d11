<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="utf-8" />
    <title>Sensi | {{ $title ?? "Home" }}</title>
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.png') }}">
    <meta property="og:title" content="Sensi | Asset Management" />
    <meta property="og:image" content="{{ asset('assets/images/favicon.png') }}" />
    <meta property="og:description" content="Aplikasi Management Asset & Logistik rumah sakit" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />

    <!-- ================== BEGIN core-css ================== -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
    <link href="{{ asset('/') }}css/vendor.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/default/app.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="{{ asset('/') }}plugins/toastify/toastify.css">
    <!-- ================== END core-css ================== -->
    <script src="{{ asset('/') }}js/vendor.min.js"></script>

    @stack("style")
</head>

<body>
    <div id="loader" class="app-loader">
        <span class="spinner"></span>
    </div>

    <div id="app" class="app">
        <x-navbar></x-navbar>

        <div id="content" class="app-content" style="margin-left: 0">
            @yield("content")
        </div>

        <a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    </div>

    <!-- ================== BEGIN core-js ================== -->
    <script src="{{ asset('/') }}js/app.min.js"></script>
    <script src="{{ asset('/') }}js/theme/default.min.js"></script>
    <script src="{{ asset('/') }}plugins/toastify/toastify.js"></script>
    <!-- ================== END core-js ================== -->

    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function successMessage(message) {
            Toastify({
                text: message,
                duration: 2000,
                style: {
                    background: "#2e7d32"
                }
            }).showToast();
        }

        function errorMessage(message) {
            Toastify({
                text: message,
                duration: 2000,
                style: {
                    background: "#c62828"
                }
            }).showToast();
        }
    </script>

    @if(session("success"))
    <script>
        successMessage("{{ session('success') }}");
    </script>
    @endif

    @if(session("error"))
    <script>
        errorMessage("{{ session('error') }}");
    </script>
    @endif

    @stack("script")
</body>

</html> 