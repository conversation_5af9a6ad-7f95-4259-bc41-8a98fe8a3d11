<a href="#modal-dialog" class="btn btn-primary" data-bs-toggle="modal" id="btn-add"><i class="fas fa-plus-circle me-1"></i>
    Tambah Ruangan</a>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Ruangan</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('master-data.room.store') }}" method="post" id="formdata">
                    <div class="form-group mb-3">
                        <label class="form-label">Kode Ruangan</label>
                        <input type="text" class="form-control" name="kode_ruangan" id="kode_ruangan">

                        <div class="d-block text-danger error-message" id="error_kode_ruangan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Kategori Ruangan</label>
                        <select id="kategori-ruangan" class="form-select" name="kategori_ruangan">
                        </select>

                        <div class="d-block text-danger error-message" id="error_kategori_ruangan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Nama Ruangan</label>
                        <input type="text" class="form-control" name="nama_ruangan" id="nama_ruangan">

                        <div class="d-block text-danger error-message" id="error_nama_ruangan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Nama Gedung</label>
                        <input type="text" class="form-control" name="nama_gedung" id="nama_gedung">

                        <div class="d-block text-danger error-message" id="error_nama_gedung"></div>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">Scanner ID</label>
                        <input type="text" class="form-control" name="device_id" id="device_id">

                        <div class="d-block text-danger error-message" id="error_device_id"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Penanggung Jawab</label>
                        <select name="penanggung_jawab" id="penanggung_jawab" class="form-select penanggung_jawab">
                            <option value="">-- Pilih Penanggung Jawab --</option>

                        </select>

                        <div class="d-block text-danger error-message" id="error_penanggung_jawab"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Divisi</label>
                        <select id="division_id" class="form-select" name="division_id">
                            <option value="">-- Pilih Divisi --</option>
                            @foreach(\App\Models\Division::where('active', 1)->get() as $division)
                                <option value="{{ $division->id }}">{{ $division->division_name }}</option>
                            @endforeach
                        </select>
                        <div class="d-block text-danger error-message" id="error_division_id"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Batal</a>
                <a href="javascript:;" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</a>
            </div>
        </div>
    </div>
</div>
