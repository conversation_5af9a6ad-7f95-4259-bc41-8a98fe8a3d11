@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
@if ($errors->any())
<div class="alert alert-danger alert-dismissible fade show">
    <strong>Error!</strong> <br>
    @foreach ($errors->all() as $error)
    <li>{{ $error }}</li>
    @endforeach
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex justify-content-end mb-3">
            <!-- Download Document -->
            <a href="#modal-download-doc" class="btn btn-danger me-1" data-bs-toggle="modal"><i class="fas fa-download me-1"></i>Download Template</a>

            <div class="modal fade" id="modal-download-doc">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Download Template</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <form action="{{ route('asset-management.asset-document.request_document') }}" method="post"
                            id="form-download-doc">
                            <div class="modal-body row">
                                @csrf
                                <div class="form-group col-md-6 mb-3">
                                    <label class="form-label">Template</label>
                                    <select name="template" id="template" class="form-select">
                                        <option disabled selected>-- Pilih Template --</option>
                                        <option value="mutasi">BAST Mutasi</option>
                                    </select>
                                </div>

                                <div class="form-group col-md-6 mb-3">
                                    <label for="target_ruangan" class="form-label">Target Ruangan</label>
                                    <select name="target_ruangan" id="target_ruangan" class="form-select target_ruangan"></select>
                                </div>

                                <div class="form-group col-md-6 mb-3">
                                    <label for="pihak_pertama" class="form-label">Pihak Pertama</label>
                                    <select name="pihak_pertama" id="pihak_pertama" class="form-select pihak_pertama"></select>
                                </div>

                                <div class="form-group col-md-6 mb-3">
                                    <label for="pihak_kedua" class="form-label">Pihak Kedua</label>
                                    <select name="pihak_kedua" id="pihak_kedua" class="form-select pihak_kedua"></select>
                                </div>

                                <div class="form-group">
                                    <table class="table table-bordered table-striped w-100 mb-3">
                                        <thead>
                                            <tr>
                                                <th>Kode Asset</th>
                                                <th>Kode QR</th>
                                                <th>Kode Register / SN</th>
                                                <th class="text-center">#</th>
                                            </tr>
                                        </thead>
                                        <tbody id="table-download-doc"></tbody>
                                    </table>

                                    <div class="d-flex justify-content-end mb-3">
                                        <button type="button" class="btn btn-outline-primary" id="btn-add-row-download"><i class="fas fa-plus-circle me-1"></i>Tambah Baris</button>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                                <button type="submit" class="btn btn-danger" id="btn-download-doc">
                                    <i class="fas fa-download me-1">
                                    </i>Download Dokumen
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Upload Document -->
            <a href="#modal-upload-doc" class="btn btn-primary" data-bs-toggle="modal"><i class="fas fa-upload me-1"></i>Upload Dokumen</a>

            <div class="modal fade" id="modal-upload-doc">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Upload Dokumen</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <form action="{{ route('asset-management.asset-document.upload') }}" method="post" id="form-upload-doc" enctype="multipart/form-data">
                            <div class="modal-body row">
                                @csrf
                                <div class="form-group col-md-6 mb-3">
                                    <label class="form-label">Tipe</label>
                                    <select name="tipe" id="tipe" class="form-select">
                                        <option disabled selected>-- Pilih Tipe --</option>
                                        <option value="mutasi">BAST Mutasi</option>
                                    </select>

                                    @error("tipe")
                                    <span class="d-block text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="form-group col-md-6 mb-3 d-none" id="document_code_field">
                                    <label class="form-label">Kode Dokumen / No. Dokumen <small class="text-secondary d-block">*Tekan enter untuk memunculkan asset.</small></label>
                                    <input type="text" name="kode_dokumen" id="kode_dokumen" class="form-control text-uppercase">

                                    @error("kode_dokumen")
                                    <span class="d-block text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="form-group col-md-12 mb-3 d-none" id="document_field">
                                    <label class="form-label">Dokumen</label>
                                    <input type="file" name="dokumen" id="dokumen" accept="application/pdf" class="form-control">

                                    @error("dokumen")
                                    <span class="d-block text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="form-group col-md-12">
                                    <div class="d-flex justify-content-end mb-3">
                                        <button type="button" class="btn btn-outline-primary d-none" id="btn-add-row-upload"><i class="fas fa-plus-circle me-1"></i>Tambah Baris</button>
                                    </div>

                                    <table class="table table-bordered table-striped w-100 d-none" id="table-upload">
                                        <thead>
                                            <tr>
                                                <th>Kode Asset</th>
                                                <th>Kode QR</th>
                                                <th>Kode Register / SN</th>
                                                <th class="text-center">#</th>
                                            </tr>
                                        </thead>
                                        <tbody id="table-upload-doc"></tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <a href="javascript:;" class="btn btn-white" id="btn-close-upload" data-bs-dismiss="modal">Close</a>
                                <button type="submit" class="btn btn-primary" id="btn-upload-doc"><i class="fas fa-upload me-1"></i>Upload Dokumen</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-striped w-100" id="datatable">
                <thead>
                    <tr>
                        <th rowspan="2" class="align-middle">#</th>
                        <th rowspan="2" class="align-middle">Foto</th>
                        <th rowspan="2" class="align-middle">Kode QR</th>
                        <th rowspan="2" class="align-middle">Nama Barang</th>
                        <th rowspan="2" class="align-middle">Kode Barang</th>
                        <th rowspan="2" class="align-middle">Kode Register / SN</th>
                        <th rowspan="2" class="align-middle">Peruntukan</th>
                        <th colspan="5" class="text-center">Dokumen BAST</th>
                    </tr>
                    <tr>
                        <th>Ceklis</th>
                        <th>Penempatan</th>
                        <th>Pembayaran</th>
                        <th>Mutasi</th>
                        <th>Aset Rusak</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-document-preview">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Preview Dokumen</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body" id="list-doc">

            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-img-preview">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <img src="" class="img-asset-preview" alt="">
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/asset-management/assetMutation.js') }}"></script>
@endpush
