<?php

namespace App\Http\Controllers\MasterData;

use App\Models\Category;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Imports\MasterCategoryImport;
use App\Exports\CategoryTemplateExport;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class CategoryController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Kategori Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Kategori Barang";
        $breadcrumbs = ["Master Data", "Kategori"];

        return view("master-data.category.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Category::select("categories.*", "employees.employee_name")
                ->leftJoin("employees", "categories.pic_category", "=", "employees.id")
                ->orderBy("category_name", "ASC");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-edit" data-bs-toggle="modal" data-route="' . route("master-data.category.show", $row->id) . '"><i class="fas fa-edit"></i></a> <button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("master-data.category.destroy", $row->id) . '"><i class="fas fa-trash"></i></button>';
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_kategori" => "required|string|unique:categories,category_code",
            "nama_kategori" => "required|string",
            "tipe_kategori" => "required|string",
            "sub_tipe_kategori" => "required|string",
            "kategori_pic" => "required|string",
        ]);

        try {
            DB::beginTransaction();

            Category::create([
                "category_code" => $request->kode_kategori,
                "category_name" => $request->nama_kategori,
                "category_type" => $request->tipe_kategori,
                "category_sub_type" => $request->sub_tipe_kategori,
                "pic_category" => $request->kategori_pic,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    // Import from excel
    function import(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "excel_file" => "required|mimes:xlsx,xls|max:5120", // Max 5MB
        ], [
            "excel_file.required" => "File Excel wajib dipilih",
            "excel_file.mimes" => "Format file harus .xlsx atau .xls",
            "excel_file.max" => "Ukuran file maksimal 5MB"
        ]);

        try {
            DB::beginTransaction();

            $file = $request->file("excel_file");
            $import = new MasterCategoryImport();

            // Log import start
            Log::info('Category import started', [
                'file_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'user_id' => getAuthUserId()
            ]);

            Excel::import($import, $file);

            // Get import summary
            $summary = $import->getImportSummary();

            // Log import completion
            Log::info('Category import completed', array_merge($summary, [
                'user_id' => getAuthUserId()
            ]));

            DB::commit();

            // Prepare response message
            $message = "Import berhasil diselesaikan. ";
            $message .= "Berhasil: {$summary['success_count']} data, ";
            $message .= "Gagal: {$summary['error_count']} data, ";
            $message .= "Dilewati: {$summary['skipped_count']} data.";

            $responseData = [
                "message" => $message,
                "summary" => $summary
            ];

            // If there are errors, include them in response
            if ($summary['error_count'] > 0) {
                $responseData['errors'] = array_slice($summary['errors'], 0, 10); // Limit to first 10 errors
                $responseData['has_more_errors'] = count($summary['errors']) > 10;
            }

            // If there are skipped rows, include them in response
            if ($summary['skipped_count'] > 0) {
                $responseData['skipped_rows'] = array_slice($summary['skipped_rows'], 0, 5); // Limit to first 5 skipped
                $responseData['has_more_skipped'] = count($summary['skipped_rows']) > 5;
            }

            return response()->json($responseData, 200);

        } catch (\Throwable $th) {
            DB::rollBack();

            // Log the error
            Log::error('Category import failed', [
                'error' => $th->getMessage(),
                'file' => $th->getFile(),
                'line' => $th->getLine(),
                'user_id' => getAuthUserId()
            ]);

            return response()->json([
                "message" => "Terjadi kesalahan saat import: " . $th->getMessage(),
                "error_details" => [
                    "file" => basename($th->getFile()),
                    "line" => $th->getLine()
                ]
            ], 500);
        }
    }

    public function show(Category $category)
    {
        $dataEmployee = optional(Employee::find($category->pic_category));
        $category->employee_name = $dataEmployee->employee_name ?? "";
        $category->employee_identification_number = $dataEmployee->employee_identification_number ?? "";

        return response()->json([
            "data" => $category
        ], 200);
    }

    function update(Request $request, Category $category)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_kategori" => "required|string",
            "nama_kategori" => "required|string",
            "tipe_kategori" => "required|string",
            "sub_tipe_kategori" => "required|string",
            "kategori_pic" => "required|string",
        ]);

        try {
            DB::beginTransaction();

            $category->update([
                "category_code" => $request->kode_kategori,
                "category_name" => $request->nama_kategori,
                "category_type" => $request->tipe_kategori,
                "category_sub_type" => $request->sub_tipe_kategori,
                "pic_category" => $request->kategori_pic,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function destroy(Category $category)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $category->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        $categories = Category::search()
            ->paginate(25);

        return response()->json($categories);
    }

    // Download template Excel untuk import
    function downloadTemplate()
    {
        if (!hasPermissionInGuard('Data Kategori Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        try {
            $fileName = 'template_import_kategori_' . date('Y-m-d_H-i-s') . '.xlsx';

            Log::info('Category template download', [
                'file_name' => $fileName,
                'user_id' => getAuthUserId()
            ]);

            return Excel::download(new CategoryTemplateExport(), $fileName);

        } catch (\Throwable $th) {
            Log::error('Category template download failed', [
                'error' => $th->getMessage(),
                'user_id' => getAuthUserId()
            ]);

            return response()->json([
                'message' => 'Gagal mendownload template: ' . $th->getMessage()
            ], 500);
        }
    }
}
