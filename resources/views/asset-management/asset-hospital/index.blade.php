@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex justify-content-end mb-3">
            <a href="{{ route('asset-management.asset-hospital.create') }}" class="btn btn-primary me-1"><i class="fas fa-plus-circle me-1"></i>Tambah Aset</a>
            <a href="#modal-dialog-import" class="btn btn-info me-1" data-bs-toggle="modal"><i class="fas fa-upload me-1"></i>Import Aset</a>
            <!-- <a href="{{ route('asset-management.asset-hospital.allocation') }}" class="btn btn-success me-1"><i class="fas fa-forward me-1"></i>Alokasi Aset</a> -->
            <a href="{{ route('asset-management.asset-hospital.print_label') }}" class="btn btn-secondary"><i class="fas fa-print me-1"></i>Print Label</a>
        </div>

        <div class="row mb-3">
            <div class="form-group col-md-4">
                <label for="kategori" class="form-label"><i>Filter Data</i></label>
                <select name="kategori" id="kategori" class="form-select">
                    <option value="all">Semua Data Aset</option>
                    <option value="not-allocation">Belum di Alokasikan</option>
                </select>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-striped w-100" id="datatable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Foto</th>
                        <th class="text-center" width="7%">Kode QR</th>
                        <th>Nama Barang</th>
                        <th>Kode Barang</th>
                        <th>Kode Register / SN</th>
                        <th>Tanggal Pembayaran</th>
                        <th>Tanggal Barang Masuk</th>
                        <th>Tahun</th>
                        <th>Asal Perolehan</th>
                        <th>Peruntukan</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>


{{-- Import Data Aset --}}
<div class="modal fade" id="modal-dialog-import">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Import Data Aset</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <form action="" method="post" enctype="multipart/form-data" id="form-import-asset">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="file-import" class="form-label">File</label>
                        <input type="file" class="form-control" id="file-import" name="file_import">

                        <span class="d-block text-danger" id="error_file_import"></span>
                    </div>
                </div>
                <div class=" modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                    <a href="{{ route('asset-management.asset-hospital.template.download') }}" class="btn btn-info"><i class="fas fa-download me-1"></i>Download Format</a>
                    <button type="button" class="btn btn-success" id="btn-import-asset"><i class="fas fa-upload"></i> Import</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-img-preview">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <img src="" class="img-asset-preview" alt="">
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/asset-management/assetHospital.js') }}"></script>
@endpush
