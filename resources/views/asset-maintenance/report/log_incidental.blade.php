@extends('layouts.app')

@push('menu')
    @include('menu.asset_maintenance')
@endpush

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
        rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
@endpush

@section('content')
    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
        </div>
        <div class="panel-body">
            <div class="">
                <div class="d-lg-flex mb-lg-3 mb-2">
                    <h1 class="page-header mb-0 flex-1"></h1>
                    <span class="d-none d-lg-flex align-items-center">
                        {{-- <a href="#" class="btn btn-secondary btn-sm d-flex me-2 pe-3 rounded-3">
							<i class="fa fa-print"></i> &nbsp; Print Result
						</a> --}}
                        <button id="btn_export_excel" data-export-filename="Laporan Insidental "
                            data-export-url="{{ route('maintenance-asset.laporan.report.export_incidental') }}"
                            data-params="{'start_date': '', 'end_date': '', 'room_id': ''}"
                            class="btn btn-secondary btn-sm d-flex me-2 pe-3 rounded-3" disabled>
                            <i class="fa fa-file-excel"></i> &nbsp; Export Excel
                        </button>

                    </span>
                </div>
                <div class="row gx-2 pb-10px">
                    <div class="col-lg-3 report_qr">
                        <label class="form-label"><i>Ruangan</i></label>
                        <select id="room_id" class="form-select room_id"></select>
                    </div>
                    <div class="col-lg-3 d-lg-block d-none">
                        <label class="form-label"><i>Periode</i></label>
                        <input id="datepicker" type="text" class="form-control" readonly="readonly"
                            style="background-color:white; cursor:pointer;">
                    </div>
                    <div class="col-lg-2 d-lg-block d-none">
                        <button style="margin-top: 22px;" type="button" id="btn_show_report"
                            class="btn btn-primary">Tampilkan Data</button>
                    </div>
                </div>
            </div>
            <hr>
            <div class="mt-3" id="report_page"></div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/moment/moment.js"></script>
    <script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="{{ asset('/js/app/asset-maintenance/app.js') }}"></script>
    <script src="{{ asset('/js/app/asset-maintenance/report.js') }}"></script>
@endpush
