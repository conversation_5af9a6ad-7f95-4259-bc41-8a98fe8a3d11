<button class="btn btn-primary" id="btn-add">
    <i class="fas fa-plus me-1"></i>Tambah Program
</button>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Program Ruangan</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <form action="{{ route('master-data.room-program.store', $room->id) }}" method="post" id="formdata">
                @csrf
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="program_ids">Program</label>
                        <select name="program_ids[]" id="program_ids" class="form-select program_ids" multiple>
                        </select>
                        <span class="text-danger error-message" id="error_program_ids"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                    <button type="submit" class="btn btn-primary" id="btn-save">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div> 