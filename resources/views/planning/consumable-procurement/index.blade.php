@extends("layouts.app")

@push('style')
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
    rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.planning")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex justify-content-end">
            <!-- <div class="widget-list-action">
                <a href="#" data-bs-toggle="dropdown" class="btn btn-primary" aria-expanded="false"><i class="fa fa-plus-circle me-1"></i>Buat Perencanaan</a>
                <div class="dropdown-menu dropdown-menu-end">
                    <a href="{{ route('planning.consumable-procurement.create') }}?type=asset" class="dropdown-item">Perencanaan Aset</a>
                    <a href="{{ route('planning.consumable-procurement.create') }}?type=logistic" class="dropdown-item">Perencanaan Logistik</a>
                    <a href="{{ route('planning.consumable-procurement.create') }}?type=other" class="dropdown-item">Perencanaan Lainnya</a>
                </div>
            </div> -->

            <a href="{{ route('planning.consumable-procurement.create') }}" class="btn btn-primary"><i class="fas fa-plus-circle me-1"></i>Buat Perencanaan</a>
        </div>

        <div class="row d-flex mb-3">
            <div class="form-group col-md-2">
                <label for="status" class="form-label"><i>Status</i></label>
                <select name="status" id="status" class="form-select">
                    <option value="all">Semua</option>
                    <option value="approved">Disetujui</option>
                    <option value="notapproved">Belum Disetujui</option>
                </select>
            </div>
            <div class="form-group col-md-2">
                <label for="year" class="form-label"><i>Tahun</i></label>
                <select name="year" id="year" class="form-select">
                    @php
                        $currentYear = date('Y');
                        for($i = $currentYear - 10; $i <= $currentYear + 2; $i++) {
                            $selected = ($i == $currentYear) ? 'selected' : '';
                            echo "<option value='$i' $selected>$i</option>";
                        }
                    @endphp
                </select>
            </div>
            <div class="form-group col-md-3">
                <label for="room" class="form-label"><i>Ruangan</i></label>
                <select name="room" id="room" class="form-select">
                    <option value="all">Semua</option>
                </select>
            </div>
            <div class="form-group col-md-2 d-flex align-items-end">
                <button type="button" id="btn-show-data" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>Tampilkan Data
                </button>
            </div>
        </div>

        <table class="table table-bordered table-striped w-100" id="datatable">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Judul Perencanaan</th>
                    <th>Ruangan</th>
                    <th>Tanggal Dibuat</th>
                    <th>Tahun Perencanaan</th>
                    <th>Total Qty</th>
                    <th>Status</th>
                    <th>Action</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Perencanaan</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body" id="target-body">

            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/planning/consumableProcurement.js') }}"></script>
@endpush