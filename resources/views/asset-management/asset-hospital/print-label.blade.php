@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
        rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        @media print {
            @page {
                size: A4;
                margin: 10mm;
            }
            body {
                margin: 0;
                padding: 0;
            }
            #target-print {
                display: flex;
                flex-wrap: wrap;
                gap: 5mm;
                justify-content: flex-start;
                width: 100%;
                padding: 5mm;
            }
            .mb-3 {
                width: calc(50% - 2.5mm);
                margin: 0 !important;
                page-break-inside: avoid;
            }
            .card {
                border: 1px solid #000;
                margin: 0;
                height: 100%;
            }
            .card-body {
                padding: 2mm;
                display: flex;
                gap: 2mm;
                height: 100%;
            }
            .border-end {
                border-right: 1px solid #000 !important;
                padding-right: 2mm;
                width: 25%;
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .border-end img.logo {
                margin-bottom: 1mm;
                width: 35px;
                height: auto;
            }
            .border-end .qr-wrapper {
                margin-top: auto;
                margin-bottom: 2mm;
            }
            .border-end .qr-wrapper img {
                width: 60px;
                height: auto;
            }
            .text-center.flex-grow-1 {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                padding: 2mm 0;
            }
            .text-center.flex-grow-1 .border-bottom {
                border-bottom: 1px solid #000 !important;
                padding-bottom: 1mm;
                margin-bottom: 1mm;
                width: 100%;
            }
            img {
                max-width: 45px;
                height: auto;
            }
        }

        /* Style untuk tampilan normal */
        #target-print {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 10px;
        }
        .mb-3 {
            width: calc(50% - 5px);
            margin: 0 !important;
        }
    </style>
@endpush

@push('menu')
    @include('menu.asset_management')
@endpush

@section('content')
    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
        </div>
        <div class="panel-body">
            <div class="row d-flex mb-5">
                <div class="form-group col-md-4">
                    <label for="ruangan" class="form-label"><i>Ruangan</i></label>
                    <select name="ruangan" id="ruangan" class="form-select ruangan"></select>
                </div>

                <div class="form-group col-md-4">
                    <label for="asset" class="form-label"><i>Asset</i></label>
                    <select name="asset" id="asset" class="form-select asset"></select>
                </div>

                <div class="form-group col-md-4">
                    <button type="button" class="btn btn-primary mt-20px" id="btn-print"><i
                            class="fas fa-print me-1"></i>Print Label</button>
                </div>
            </div>

            <div class="row" id="target-print"></div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/asset-management/printLabel.js') }}"></script>
@endpush
