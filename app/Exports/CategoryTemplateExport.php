<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class CategoryTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        return [
            [
                'KTG001',
                'Elektronik',
                'Peralatan',
                'Komputer',
                'EMP001'
            ],
            [
                'KTG002',
                'Furniture',
                'Perabotan',
                'Meja Ku<PERSON>i',
                'EMP002'
            ],
            [
                'KTG003',
                'Kendaraan',
                'Transportasi',
                'Motor',
                ''
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'kode_kategori',
            'nama_kategori',
            'tipe_kategori',
            'sub_tipe_kategori',
            'pic_kategori'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
            ],
            // Style data rows
            '2:4' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // kode_kategori
            'B' => 25, // nama_kategori
            'C' => 20, // tipe_kategori
            'D' => 20, // sub_tipe_kategori
            'E' => 15, // pic_kategori
        ];
    }

    public function title(): string
    {
        return 'Template Import Kategori';
    }
}