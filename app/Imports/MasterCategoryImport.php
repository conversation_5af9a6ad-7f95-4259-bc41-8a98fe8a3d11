<?php

namespace App\Imports;

use App\Models\Category;
use App\Models\Employee;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Validators\Failure;
use Throwable;

class MasterCategoryImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable;

    protected $rowIndex = 1;
    protected $successCount = 0;
    protected $errorCount = 0;
    protected $errors = [];
    protected $skippedRows = [];

    public function model(array $row)
    {
        $this->rowIndex++;

        // Skip empty rows
        if (empty(array_filter($row))) {
            return null;
        }

        try {
            // Validate required fields
            if (empty($row['kode_kategori'])) {
                throw new \Exception("Kode kategori tidak boleh kosong");
            }

            if (empty($row['nama_kategori'])) {
                throw new \Exception("Nama kategori tidak boleh kosong");
            }

            if (empty($row['tipe_kategori'])) {
                throw new \Exception("Tipe kategori tidak boleh kosong");
            }

            if (empty($row['sub_tipe_kategori'])) {
                throw new \Exception("Sub tipe kategori tidak boleh kosong");
            }

            // Check if category code already exists
            $existingCategory = Category::where('category_code', $row['kode_kategori'])->first();
            if ($existingCategory) {
                $this->skippedRows[] = [
                    'row' => $this->rowIndex,
                    'reason' => "Kode kategori '{$row['kode_kategori']}' sudah ada",
                    'data' => $row
                ];
                return null;
            }

            // Find PIC employee if provided
            $picCategoryId = null;
            if (!empty($row['pic_kategori'])) {
                $employee = Employee::where('employee_identification_number', $row['pic_kategori'])
                    ->orWhere('employee_name', 'LIKE', '%' . $row['pic_kategori'] . '%')
                    ->first();
                
                if (!$employee) {
                    throw new \Exception("PIC kategori '{$row['pic_kategori']}' tidak ditemukan");
                }
                $picCategoryId = $employee->id;
            }

            $this->successCount++;

            return new Category([
                'category_code' => $row['kode_kategori'],
                'category_name' => $row['nama_kategori'],
                'category_type' => $row['tipe_kategori'],
                'category_sub_type' => $row['sub_tipe_kategori'],
                'pic_category' => $picCategoryId,
                'created_by' => getAuthUserId()
            ]);

        } catch (\Exception $e) {
            $this->errorCount++;
            $this->errors[] = [
                'row' => $this->rowIndex,
                'error' => $e->getMessage(),
                'data' => $row
            ];
            
            Log::error("Import Category Error - Row {$this->rowIndex}: " . $e->getMessage(), [
                'row_data' => $row,
                'user_id' => getAuthUserId()
            ]);
            
            return null;
        }
    }

    public function rules(): array
    {
        return [
            'kode_kategori' => 'required|string|max:50',
            'nama_kategori' => 'required|string|max:255',
            'tipe_kategori' => 'required|string|max:100',
            'sub_tipe_kategori' => 'required|string|max:100',
            'pic_kategori' => 'nullable|string|max:255',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'kode_kategori.required' => 'Kode kategori wajib diisi',
            'kode_kategori.max' => 'Kode kategori maksimal 50 karakter',
            'nama_kategori.required' => 'Nama kategori wajib diisi',
            'nama_kategori.max' => 'Nama kategori maksimal 255 karakter',
            'tipe_kategori.required' => 'Tipe kategori wajib diisi',
            'tipe_kategori.max' => 'Tipe kategori maksimal 100 karakter',
            'sub_tipe_kategori.required' => 'Sub tipe kategori wajib diisi',
            'sub_tipe_kategori.max' => 'Sub tipe kategori maksimal 100 karakter',
            'pic_kategori.max' => 'PIC kategori maksimal 255 karakter',
        ];
    }

    public function onError(Throwable $e)
    {
        $this->errorCount++;
        $this->errors[] = [
            'row' => $this->rowIndex,
            'error' => $e->getMessage(),
            'data' => []
        ];
    }

    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            $this->errorCount++;
            $this->errors[] = [
                'row' => $failure->row(),
                'error' => implode(', ', $failure->errors()),
                'data' => $failure->values()
            ];
        }
    }

    public function getImportSummary()
    {
        return [
            'total_processed' => $this->rowIndex - 1,
            'success_count' => $this->successCount,
            'error_count' => $this->errorCount,
            'skipped_count' => count($this->skippedRows),
            'errors' => $this->errors,
            'skipped_rows' => $this->skippedRows
        ];
    }
}