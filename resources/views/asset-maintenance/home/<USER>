@extends("layouts.app")

@push("style")
<style>
    .widget.widget-stats {
        position: relative;
        padding: 15px;
        border-radius: 4px;
    }
    .widget.widget-stats .stats-info h4 {
        font-size: .875rem;
        margin: 5px 0;
    }
    .widget.widget-stats .stats-icon {
        font-size: 3.5em;
        height: 56px;
        width: 56px;
        text-align: center;
        line-height: 56px;
        margin-left: 15px;
        position: absolute;
        right: 15px;
        top: 15px;
        opacity: .2;
    }
</style>
@endpush

@push("menu")
@include("menu.asset_maintenance")
@endpush

@section("content")
<div class="row">
    <!-- BEGIN col-6 -->
    <div class="col-xl-6">
        <!-- BEGIN card -->
        <div class="card border-0 mb-3">
            <!-- BEGIN card-body -->
            <div class="card-body">
                <!-- BEGIN row -->
                <div class="row">
                    <div class="col-xl-7 col-lg-8">
                        <!-- BEGIN title -->
                        <div class="mb-3">
                            <b>REQUEST PERBAIKAN OPEN</b>
                            <span class="ms-2">
                                <i class="fa fa-info-circle" data-bs-toggle="popover" data-bs-trigger="hover" 
                                   data-bs-title="Request Perbaikan" data-bs-placement="top" 
                                   data-bs-content="Total request perbaikan yang masih open"></i>
                            </span>
                        </div>
                        <!-- END title -->
                        <div class="d-flex mb-1">
                            <h2 class="mb-0">{{ number_format($openRequestCount) }}</h2>
                        </div>
                        <hr>
                        <div class="row text-truncate">
                            <div class="col-6">
                                <div>Pemeliharaan Alkes</div>
                                <div class="fs-18px mb-5px fw-bold">{{ number_format($alkesMaintenanceCount) }}</div>
                            </div>
                            <div class="col-6">
                                <div>Pemeliharaan Non-Alkes</div>
                                <div class="fs-18px mb-5px fw-bold">{{ number_format($nonAlkesMaintenanceCount) }}</div>
                            </div>
                        </div>
                    </div>
                    <!-- BEGIN col-5 -->
                    <div class="col-xl-5 col-lg-4 align-items-center d-flex justify-content-center">
                        <img src="../img/svg/img-1.svg" height="150px" class="d-none d-lg-block">
                    </div>
                    <!-- END col-5 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Maintenance Schedule -->
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">Jadwal Pemeliharaan Bulan Ini</h4>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Kode Jadwal</th>
                        <th>Tanggal</th>
                        <th>Kategori</th>
                        <th>Tipe</th>
                        <th>Item</th>
                        <th>Jumlah</th>
                        <th>Catatan</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($maintenanceSchedules as $schedule)
                    <tr>
                        <td>{{ $schedule->schedule_code }}</td>
                        <td>{{ $schedule->schedule_date }}</td>
                        <td>{{ $schedule->schedule_category }}</td>
                        <td>{{ $schedule->schedule_type }}</td>
                        <td>{{ $schedule->item_name }}</td>
                        <td>{{ $schedule->schedule_quantity }}</td>
                        <td>{{ $schedule->schedule_notes }}</td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center">Tidak ada jadwal pemeliharaan</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection
