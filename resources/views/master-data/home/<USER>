@extends("layouts.app")

@push("style")
<style>
    .stats-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        padding: 1rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        border: none;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }

    .stats-icon.rooms { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .stats-icon.employees { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .stats-icon.distributors { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); }
    .stats-icon.uoms { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.2rem;
    }

    .stats-label {
        color: #7f8c8d;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .stats-link {
        color: #1e3c72;
        text-decoration: none;
        font-weight: 500;
        font-size: 0.9rem;
        transition: color 0.3s ease;
        text-align: right;
        display: block;
    }

    .stats-link:hover {
        color: #2a5298;
        text-decoration: underline;
    }

    .data-panel {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border: none;
        margin-bottom: 1.5rem;
    }

    .data-panel .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        border-radius: 15px 15px 0 0 !important;
        padding: 1rem 1.5rem;
    }

    .data-panel .card-title {
        color: #2c3e50;
        font-weight: 700;
        margin: 0;
        font-size: 1.1rem;
    }

    .data-panel .card-body {
        padding: 1.5rem;
    }

    .table-modern {
        border-radius: 10px;
        overflow: hidden;
    }

    .table-modern thead th {
        background: #f8f9fa;
        border: none;
        color: #2c3e50;
        font-weight: 600;
        padding: 1rem;
        font-size: 0.9rem;
    }

    .table-modern tbody td {
        border: none;
        padding: 1rem;
        vertical-align: middle;
        color: #2c3e50;
    }

    .table-modern tbody tr {
        border-bottom: 1px solid #f1f3f4;
        transition: background-color 0.3s ease;
    }

    .table-modern tbody tr:hover {
        background-color: #f8f9fa;
    }

    .empty-state {
        text-align: center;
        padding: 2rem;
        color: #7f8c8d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
@endpush

@push("menu")
@include("menu.master_data")
@endpush

@section("content")
<!-- Stats Overview -->
<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon rooms me-3">
                    <i class="fa fa-building"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stats-number">{{ number_format($totalRooms ?? 0) }}</div>
                    <div class="stats-label">Total Ruangan</div>
                    <a href="{{ route('master-data.room.index') }}" class="stats-link">
                        Lihat Detail <i class="fa fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon employees me-3">
                    <i class="fa fa-users"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stats-number">{{ number_format($totalEmployees ?? 0) }}</div>
                    <div class="stats-label">Total Pegawai</div>
                    <a href="{{ route('master-data.employee.index') }}" class="stats-link">
                        Lihat Detail <i class="fa fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon distributors me-3">
                    <i class="fa fa-truck"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stats-number">{{ number_format($totalDistributors ?? 0) }}</div>
                    <div class="stats-label">Total Distributor</div>
                    <a href="{{ route('master-data.distributor.index') }}" class="stats-link">
                        Lihat Detail <i class="fa fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon uoms me-3">
                    <i class="fa fa-cube"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stats-number">{{ number_format($totalUoms ?? 0) }}</div>
                    <div class="stats-label">Total UOM</div>
                    <a href="{{ route('master-data.uom.index') }}" class="stats-link">
                        Lihat Detail <i class="fa fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Data -->
<div class="row">
    <!-- Latest Rooms -->
    <div class="col-xl-6">
        <div class="data-panel">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fa fa-building me-2"></i>
                    Ruangan Terbaru
                </h5>
            </div>
            <div class="card-body p-0">
                @if(count($latestRooms ?? []) > 0)
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th>Kode</th>
                                <th>Nama Ruangan</th>
                                <th>PIC</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($latestRooms as $room)
                            <tr>
                                <td><span class="badge bg-primary">{{ $room->room_code }}</span></td>
                                <td><strong>{{ $room->room_name }}</strong></td>
                                <td>{{ $room->pic?->employee_name ?? '-' }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="empty-state">
                    <i class="fa fa-building"></i>
                    <p class="mb-0">Belum ada data ruangan</p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Latest Employees -->
    <div class="col-xl-6">
        <div class="data-panel">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fa fa-users me-2"></i>
                    Pegawai Terbaru
                </h5>
            </div>
            <div class="card-body p-0">
                @if(count($latestEmployees ?? []) > 0)
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th>NIP</th>
                                <th>Nama</th>
                                <th>Jabatan</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($latestEmployees as $employee)
                            <tr>
                                <td><span class="badge bg-info">{{ $employee->employee_identification_number }}</span></td>
                                <td><strong>{{ $employee->employee_name }}</strong></td>
                                <td>{{ $employee->employee_position }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="empty-state">
                    <i class="fa fa-users"></i>
                    <p class="mb-0">Belum ada data pegawai</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
