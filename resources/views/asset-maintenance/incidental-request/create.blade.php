@extends("layouts.app")

@push("style")
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
@endpush

@push("menu")
    @include("menu.asset_maintenance")
@endpush

@section("content")
    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
        </div>
        <form action="" method="post" id="form-activity">
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="mb-6">
                            <h3 style="margin-top: 20px;">Formulir Kerusakan Barang</h3>
                        </div>
                        <div class="mt-4">
                            <label for="target_ruangan" class="form-label">Ruangan</label>
                            <select name="target_ruangan" id="target_ruangan" class="form-select target_ruangan"></select>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        {{--nomor surat--}}
                    </div>
                    <div class="col-sm-3">
                        <div class="mb-6">
                            <div class="text-sm-left">
                                Tanggal Form Request
                                <input type="date" name="activity_date" class="form-control" value="{{ old('tanggal_pembayaran') ?? now()->format('Y-m-d') }}">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3 mt-5" style="display: none">
                    <button type="button" id="btn-add-row" class="btn btn-outline-primary me-1"><i class="fas fa-plus-circle me-1"></i>Tambah Baris</button>
                </div>
                <div class="table-responsive mt-5">
                    <table class="table table-bordered table-striped w-100 mb-3">
                        <thead>
                        <tr>
                            <th style="max-width: 300px !important;">Data Aset</th>
                            <th>Nama Barang / Kode Registarsi</th>
                            <th>Brand / Type</th>
                            <th>Spesifikasi Umum</th>
                        </tr>
                        </thead>
                        <tbody id="table-allocation"></tbody>
                    </table>
                </div>

                <div class="row my-4">
                    <div class="col-md-3">
                        Jenis Kerusakan:
                        <textarea style="width:100%;;" rows="3" cols="3" name="jenis_kerusakan" class="form-control" placeholder="Jenis Kerusakan"></textarea>
                        <br>
                        <span style="cursor: pointer" id="file_attach"><i class="fa fa-file-pdf"></i> File Lampiran</span>
                        <input type="file" name="document" id="attachment_link" class="form-control">
                    </div>
                </div>

                <div class="form-group d-flex justify-content-end">
                    <button type="button" id="btn-save" class="btn btn-primary"><i class="fas fa-save me-1"></i>Simpan Form</button>
                </div>
            </div>
        </form>
    </div>
@endsection

@push("script")
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/asset-maintenance/incidentalRequest.js') }}"></script>
@endpush
