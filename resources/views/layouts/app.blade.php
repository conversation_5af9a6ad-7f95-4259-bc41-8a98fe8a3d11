<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Sensi | {{ $title ?? "" }}</title>
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.png') }}">
    <meta property="og:title" content="Sensi | Asset Management" />
    <meta property="og:image" content="{{ asset('assets/images/favicon.png') }}" />
    <meta property="og:description" content="Aplikasi Management Asset & Logistik rumah sakit" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />

    <!-- ================== BEGIN core-css ================== -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
    <link href="{{ asset('/') }}css/vendor.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/default/app.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="{{ asset('/') }}plugins/toastify/toastify.css">
    <!-- ================== END core-css ================== -->
    <script src="{{ asset('/') }}js/vendor.min.js"></script>

    <style>
        .modern-breadcrumb {
            background: transparent;
            padding: 0;
            margin: 0;
            border: none;
        }

        .modern-breadcrumb .breadcrumb-item {
            font-size: 0.75rem;
            color: #6c757d;
        }

        .modern-breadcrumb .breadcrumb-item a {
            color: #1e3c72;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .modern-breadcrumb .breadcrumb-item a:hover {
            color: #2a5298;
        }

        .modern-breadcrumb .breadcrumb-item.active {
            color: #495057;
            font-weight: 600;
        }

        .modern-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: #adb5bd;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .modern-page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding: 0;
            border: none;
            background: transparent;
        }

        .page-title-section {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .page-title-icon {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }

        .page-title-content h1 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
            line-height: 1.2;
        }

        .page-title-content p {
            color: #6c757d;
            font-size: 0.8rem;
            margin: 0.25rem 0 0 0;
        }

        .page-actions {
            display: flex;
            gap: 0.5rem;
        }

        @media (max-width: 768px) {
            .modern-page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .page-title-section {
                width: 100%;
            }

            .page-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }
    </style>

    @stack("style")
</head>

<body>
    <div id="loader" class="app-loader">
        <span class="spinner"></span>
    </div>

    <div id="app" class="app app-header-fixed app-sidebar-fixed">
        <x-navbar></x-navbar>

        <x-sidebar></x-sidebar>

        <div id="content" class="app-content">
            <!-- Modern Page Header -->
            <div class="modern-page-header">
                <div class="page-title-section">
                    <div class="page-title-icon">
                        <i class="fa fa-list"></i>
                    </div>
                    <div class="page-title-content">
                        <h1>{{ $title ?? "Blank Page" }}</h1>
                        <p>Kelola dan monitor data dengan mudah</p>
                    </div>
                </div>
                <div class="page-actions">
                    <!-- Modern Breadcrumb -->
                    <nav aria-label="breadcrumb" class="modern-breadcrumb">
                        <ol class="breadcrumb">
                            @foreach ($breadcrumbs as $breadcrumb)
                            <li class="breadcrumb-item">
                                @if($loop->last)
                                    <span class="active">{{ $breadcrumb }}</span>
                                @else
                                    <a href="javascript:;">{{ $breadcrumb }}</a>
                                @endif
                            </li>
                            @endforeach
                        </ol>
                    </nav>
                </div>
            </div>

            @yield("content")
        </div>

        <a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    </div>

    <!-- ================== BEGIN core-js ================== -->
    <script src="{{ asset('/') }}js/app.min.js"></script>
    <script src="{{ asset('/') }}js/theme/default.min.js"></script>
    <script src="{{ asset('/') }}plugins/toastify/toastify.js"></script>
    <!-- ================== END core-js ================== -->

    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function successMessage(message) {
            Toastify({
                text: message,
                duration: 2000,
                style: {
                    background: "#2e7d32"
                }
            }).showToast();
        }

        function errorMessage(message) {
            Toastify({
                text: message,
                duration: 2000,
                style: {
                    background: "#c62828"
                }
            }).showToast();
        }
    </script>

    @if(session("success"))
    <script>
        successMessage("{{ session('success') }}");
    </script>
    @endif

    @if(session("error"))
    <script>
        errorMessage("{{ session('error') }}");
    </script>
    @endif

    @stack("script")
</body>

</html>
