@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.logistic')
@endpush

@section('content')
    <style>
        .select2-results__option {
            font-size: 80%;
        }

        .select2-selection__rendered {
            font-size: 80%;
        }
    </style>

    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
        </div>
        <form action="" method="post" id="form-activity">
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="mb-6">
                            <h3 style="margin-top: 20px;">Formulir Rekap</h3>
                        </div>
                        <div class="mt-4">
                            <label for="tipe_rekap" class="form-label">Tipe Rekap</label>
                            <select name="tipe_rekap" id="tipe_rekap" class="form-select tipe_rekap"></select>
                        </div>
                    </div>
                    <div class="col-sm-6">
                    </div>
                    <div class="col-sm-3">
                        <div class="mb-6">
                            <div class="text-sm-left">
                                Tanggal Rekap
                                <input type="date" name="request_date" class="form-control"
                                    value="{{ old('tanggal_pembayaran') ?? now()->format('Y-m-d') }}">
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row my-4">
                    <div class="col-md-3">
                        <label for="judul_rekap">Judul Rekap:</label>
                        <input type="text" name="judul_rekap" id="judul_rekap" class="form-control"
                            placeholder="Masukkan Judul Rekap">
                        <br>
                        Informasi Rekap:
                        <textarea style="width:100%;" rows="3" cols="3" name="notes" class="form-control"
                            placeholder="Catatan / Deskripsi"></textarea>
                        <br>

                        <label for="total_nilai_rekapitulasi">Total Nilai Rekapitulasi:</label>
                        <input type="text" name="total_nilai_rekapitulasi" id="total_nilai_rekapitulasi"
                            class="form-control" placeholder="0" min="0">
                        <br>

                        <span style="cursor: pointer" id="file_attach"><i class="fa fa-file-excel"></i> File Laporan
                            Rekapitulasi
                            <i></i></span>
                        <input type="file" name="document" id="attachment_link" class="form-control">
                    </div>
                    <div class="col-md-6">
                    </div>

                </div>

                <div class="form-group d-flex justify-content-end">
                    <button type="button" id="btn-save" class="btn btn-primary"><i class="fas fa-save me-1"></i>Simpan
                        Form</button>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/logistic/createStockRecap.js') }}"></script>
@endpush
