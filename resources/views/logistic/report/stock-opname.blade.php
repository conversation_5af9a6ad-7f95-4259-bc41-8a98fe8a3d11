@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.logistic")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex justify-content-end mb-3">
            <button class="btn btn-secondary btn-export-excel me-2"><i class="fas fa-file-excel me-1"></i> Export Excel</button>
            <a href="javascript:;" class="btn btn-warning btn-export-pdf"><i class="fas fa-file-pdf me-1"></i> Export PDF</a>
        </div>
        <div class="row d-flex mb-3">
            <div class="form-group col-md-4">
                <label for="daterange" class="form-label"><i>Periode</i></label>
                <input type="text" id="datepicker" name="daterange" class="form-control">
            </div>

            <div class="form-group col-md-4">
                <label for="stock_recap" class="form-label"><i>Jenis Barang</i></label>
                <select class="form-select stock_recap" name="stock_recap" id="stock_recap">
                    <option value="ALL">Semua Jenis Barang</option>
                    @foreach($configStockRecap as $recap)
                        <option value="{{ $recap->id }}">{{ $recap->name }}</option>
                    @endforeach
                </select>
            </div>

            <div class="form-group col-md-4">
                <button type="button" class="btn btn-primary mt-20px" id="btn-filter">
                    <i class="fas fa-filter me-1"></i>Tampilkan
                </button>
            </div>
        </div>

        <div id="target-report">
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/moment/moment.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/logistic/stockOpname.js') }}"></script>
@endpush