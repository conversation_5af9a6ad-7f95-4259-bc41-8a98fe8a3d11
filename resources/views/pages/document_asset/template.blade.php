@extends("layouts.app")
@push("style")
<link href="{{ asset('/') }}plugins/summernote/dist/summernote-lite.css" rel="stylesheet" />
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>

    <div class="panel-body">
        <form action="" method="post" id="formdata">

            @if(request("type") == "penempatan")
            @include("template.bast_penempatan")
            @endif

            <div class="form-group mb-3">
                <button type="button" id="btn-save" class="btn btn-primary"><i class="fas fa-save me-1"></i>Save</button>
            </div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/summernote/dist/summernote-lite.min.js"></script>
<script>
    $("#template").summernote({
        placeholder: "Silahkan buat template.",
        height: "300"
    });

    $("#btn-save").on("click", function(e) {
        e.preventDefault();

        let formData = new FormData($("#formdata")[0]);

        $.ajax({
            type: "POST",
            url: "{{ route('document-bast.save_template') }}",
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                successMessage(response.message);
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        $("#" + key).addClass("is-invalid");
                        $("#error_" + key).text(value);
                    });
                } else {
                    errorMessage(xhr.responseJSON.message)
                }
            }
        });
    });

    $("#btn-save-add").on("click", function() {
        $("#add").val(1);
        $("#formdata").submit();
    });
</script>
@endpush