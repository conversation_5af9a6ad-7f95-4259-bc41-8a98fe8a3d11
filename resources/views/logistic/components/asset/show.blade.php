<table class="table table-bordered table-striped">
    <thead>
        <tr>
            <th>No</th>
            <th>Barang</th>
            <th>Aset Logistik</th>
            <th><PERSON>tu<PERSON></th>
            <th><PERSON><PERSON> (Rp.)</th>
        </tr>
    </thead>

    <tbody>
        @foreach ($assets as $asset)
        <tr>
            <td>{{ $loop->iteration }}</td>
            <td>
                {{ $asset->item->item_name }}
                <span class="d-block text-danger">{{ $asset->item->item_code }}</span>
            </td>
            <td>
                {{ $asset->asset_name }}
                <span class="d-block text-danger">{{ $asset->asset_code }}</span>
            </td>
            <td>
                {{ $asset->assetEntry->uom_name }}
            </td>
            <td>{{ number_format($asset->unit_price, 0, ",", ".") }}</td>
        </tr>
        @endforeach
    </tbody>
</table>