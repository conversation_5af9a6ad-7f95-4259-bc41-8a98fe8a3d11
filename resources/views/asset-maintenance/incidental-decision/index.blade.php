@extends("layouts.app")

@push("style")
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
@endpush

@push("menu")
    @include("menu.asset_maintenance")
@endpush

@section("content")
    <style>
        .timeline .timeline-item {
            font-size: 85%;
            position: relative;
            min-height: 20px;
            padding: 0px 0;
        }

        .timeline .timeline-body {
            padding-top: 10px;
            font-size: 85% !important;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 3px;
            bottom: 3px;
            width: 3px;
            background: #495057;
            left: 20%;
            margin-left: -2.5px;
        }
    </style>

    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
        </div>
        <div class="panel-body">
            <div class="d-flex justify-content-between mb-3">
                <select style="width: auto" name="target_ruangan" id="target_ruangan" class="form-select target_ruangan"></select>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped w-100" id="datatable">
                    <thead>
                    <tr>
                        <th>#</th>
                        <th>Kode Request</th>
                        <th>Tanggal Request</th>
                        <th>Asset</th>
                        <th>Jenis Kerusakan</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal Detail -->
    <div class="modal fade" id="modal-dialog">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Detail Request</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div id="activityBody" class="modal-body"></div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Tutup</a>
                    {{--                    <a href="javascript:;" class="btn btn-primary show-followup" id="btn-save"><i class="fas fa-save me-1"></i>Update Followup</a>--}}
                </div>
            </div>
        </div>
    </div>
    <!-- End of Modal Detail -->

    <!-- Modal Assign -->
    <div class="modal fade" id="modal-assign">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Penugasan</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <button type="button" id="btn-add-officer" class="btn btn-outline-primary me-1"><i class="fas fa-plus-circle me-1"></i>Tambah Petugas</button>
                    </div>
                    <div class="table-responsive mb-3">
                        <table class="table table-bordered table-striped w-100 mb-3">
                            <thead>
                            <tr>
                                <th style="min-width: 300px !important;">Petugas</th>
                            </tr>
                            </thead>
                            <tbody id="table-officer"></tbody>
                        </table>
                    </div>
                    Catatan Penugasan:
                    <textarea id="assign_notice" style="width:100%;;" rows="3" cols="3" name="notes" class="form-control" placeholder="Catatan / Deskripsi"></textarea>
                    <br>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-white" data-bs-dismiss="modal">Tutup</button>
                    <button id="save-assign" class="btn btn-primary"><i class="fas fa-save me-1"></i>Update Penugasan</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End of Modal Assign -->

    <!-- Modal Add BHP -->
    <div class="modal fade" id="modal-add-bhp">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">BHP</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <button type="button" id="btn-add-bhp" class="btn btn-outline-primary me-1"><i class="fas fa-plus-circle me-1"></i>Tambah BHP</button>
                    </div>
                    <div class="table-responsive mb-3">
                        <table class="table table-bordered table-striped w-100 mb-3">
                            <thead>
                            <tr>
                                <th style="min-width: 300px !important;">Nama</th>
                                <th style="min-width: 300px !important;">Harga</th>
                                <th style="min-width: 300px !important;">Qty</th>
                            </tr>
                            </thead>
                            <tbody id="table-bhp"></tbody>
                        </table>
                    </div>
                    Catatan Penugasan:
                    <textarea id="bhp_notice" style="width:100%;;" rows="3" cols="3" name="notes" class="form-control" placeholder="Catatan / Deskripsi"></textarea>
                    <br>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-white" data-bs-dismiss="modal">Tutup</button>
                    <button id="save-bhp" class="btn btn-primary"><i class="fas fa-save me-1"></i>Update Data</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End of Modal Add BHP -->

    <!-- Modal Call Vendor -->
    <div class="modal fade" id="modal-call-vendor">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Pemanggilan Vendor</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    Catatan Pemanggilan Vendor:
                    <textarea id="vendor_notice" style="width:100%;;" rows="3" cols="3" name="notes" class="form-control" placeholder="Catatan / Deskripsi"></textarea>
                    <br>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-white" data-bs-dismiss="modal">Tutup</button>
                    <button id="save-vendor" class="btn btn-primary"><i class="fas fa-save me-1"></i>Update Data</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End of Modal Call Vendor -->

    <!-- Modal Call Vendor -->
    <div class="modal fade" id="modal-final-decision">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Keputusan Akhir</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">

                    Keputusan Terkait Perbaikan:
                    <select id="final_decision_select" class="form-control">
                        <option value="FIXED">Diperbaiki</option>
                        <option value="NOT_FIXED">Tidak Diperbaiki</option>
                    </select>
                    <br>

                    Catatan Keputusan Terkait Perbaikan:
                    <textarea id="decision_notice" style="width:100%;;" rows="3" cols="3" name="notes" class="form-control" placeholder="Catatan / Deskripsi"></textarea>
                    <br>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-white" data-bs-dismiss="modal">Tutup</button>
                    <button id="save-decision" class="btn btn-primary"><i class="fas fa-save me-1"></i>Update Data</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End of Modal Call Vendor -->

@endsection

@push("script")
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/js/app/asset-maintenance/app.js') }}"></script>
    <script src="{{ asset('/js/app/asset-maintenance/incidentalDecision.js') }}"></script>
    <script src="{{ asset('/js/app/asset-maintenance/incidentalDecisionAction.js') }}"></script>
@endpush
