@extends("layouts.app")

@push("style")
<style>
    .widget.widget-stats {
        position: relative;
        padding: 15px;
        border-radius: 4px;
    }
    .widget.widget-stats .stats-info h4 {
        font-size: .875rem;
        margin: 5px 0;
    }
    .widget.widget-stats .stats-icon {
        font-size: 3.5em;
        height: 56px;
        width: 56px;
        text-align: center;
        line-height: 56px;
        margin-left: 15px;
        position: absolute;
        right: 15px;
        top: 15px;
        opacity: .2;
    }
</style>
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="row">
    <!-- BEGIN col-6 -->
    <div class="col-xl-6">
        <!-- BEGIN card -->
        <div class="card border-0 mb-3">
            <!-- BEGIN card-body -->
            <div class="card-body">
                <!-- BEGIN row -->
                <div class="row">
                    <!-- BEGIN col-7 -->
                    <div class="col-xl-7 col-lg-8">
                        <!-- BEGIN title -->
                        <div class="mb-3">
                            <b>TOTAL ASET</b>
                            <span class="ms-2">
                                <i class="fa fa-info-circle" data-bs-toggle="popover" data-bs-trigger="hover" 
                                   data-bs-title="Total Aset" data-bs-placement="top" 
                                   data-bs-content="Total aset peralatan yang aktif"></i>
                            </span>
                        </div>
                        <!-- END title -->
                        <!-- BEGIN total-sales -->
                        <div class="d-flex mb-1">
                            <h2 class="mb-0">
                                <span data-animation="number" data-value="{{ $totalEquipments }}">{{ number_format($totalEquipments) }}</span>
                            </h2>
                            <div class="ms-auto mt-n1 mb-n1">
                                <div id="total-sales-sparkline"></div>
                            </div>
                        </div>
                        <hr>
                        <!-- BEGIN row -->
                        <div class="row text-truncate">
                            <!-- BEGIN col-6 -->
                            <div class="col-6">
                                <div>Total Buku Bantu Pending</div>
                                <div class="fs-18px mb-5px fw-bold">{{ number_format($totalPendingDocuments) }}</div>
                                <div class="progress h-5px rounded-3 bg-gray-200 mb-5px">
                                    <div class="progress-bar progress-bar-striped rounded-right bg-teal" style="width: 55%"></div>
                                </div>
                            </div>
                            <!-- END col-6 -->
                            <!-- BEGIN col-6 -->
                            <div class="col-6">
                                <div>Perubahan Posisi Hari Ini</div>
                                <div class="fs-18px mb-5px fw-bold">{{ number_format($totalTodayLocationChanges) }}</div>
                                <div class="progress h-5px rounded-3 bg-gray-200 mb-5px">
                                    <div class="progress-bar progress-bar-striped rounded-right" style="width: 55%"></div>
                                </div>
                            </div>
                            <!-- END col-6 -->
                        </div>
                        <!-- END row -->
                    </div>
                    <!-- END col-7 -->
                    <!-- BEGIN col-5 -->
                    <div class="col-xl-5 col-lg-4 align-items-center d-flex justify-content-center">
                        <img src="../img/svg/img-1.svg" height="150px" class="d-none d-lg-block">
                    </div>
                    <!-- END col-5 -->
                </div>
                <!-- END row -->
            </div>
            <!-- END card-body -->
        </div>
        <!-- END card -->
    </div>
    <!-- END col-6 -->
    
    <!-- BEGIN col-6 -->
    <div class="col-xl-6">
        <!-- BEGIN row -->
        <div class="row">
            <!-- BEGIN col-6 -->
            <div class="col-sm-6">
                <!-- BEGIN card -->
                <div class="card border-0 text-truncate mb-3">
                    <!-- BEGIN card-body -->
                    <div class="card-body">
                        <!-- BEGIN title -->
                        <div class="mb-3">
                            <b class="mb-3">PERUBAHAN POSISI</b>
                            <span class="ms-2">
                                <i class="fa fa-info-circle" data-bs-toggle="popover" data-bs-trigger="hover" 
                                   data-bs-title="Perubahan Posisi" data-bs-placement="top" 
                                   data-bs-content="Jumlah perubahan posisi aset hari ini"></i>
                            </span>
                        </div>
                        <!-- END title -->
                        <div class="d-flex align-items-center mb-1">
                            <h2 class="mb-0">{{ number_format($totalTodayLocationChanges) }}</h2>
                        </div>
                    </div>
                    <!-- END card-body -->
                </div>
                <!-- END card -->
            </div>
            <!-- END col-6 -->
        </div>
        <!-- END row -->
    </div>
    <!-- END col-6 -->
</div>

<!-- Latest Location Changes -->
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">Perubahan Posisi Terakhir</h4>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Waktu Scan</th>
                        <th>Kode Aset</th>
                        <th>Nama Aset</th>
                        <th>Ruangan</th>
                        <th>Ruangan Sebelumnya</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($latestLocationChanges as $change)
                    <tr>
                        <td>{{ $change->scan_time }}</td>
                        <td>{{ $change->asset->asset_code }}</td>
                        <td>{{ $change->asset->asset_name }}</td>
                        <td>{{ $change->room->room_name }}</td>
                        <td>{{ $change->prevRoom->room_name ?? '-' }}</td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-center">Tidak ada data</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Inisialisasi sparkline dan chart lainnya bisa ditambahkan di sini
</script>
@endpush
