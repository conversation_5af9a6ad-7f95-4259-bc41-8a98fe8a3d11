@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.logistic")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <form action="" method="post" class="row" id="formdata">
            <div class="form-group col-md-4 mb-3">
                <label for="kode_barang" class="form-label">Kode Barang</label>
                <select name="kode_barang" id="kode_barang" class="form-select kode-barang"></select>

                <span class="d-block text-danger" id="error_kode_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="stok_rekapitulasi" class="form-label">Stok Rekapitulasi</label>
                <select name="stok_rekapitulasi" id="stok_rekapitulasi" class="form-select stok-rekapitulasi"></select>

                <span class="d-block text-danger" id="error_stok_rekapitulasi"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="satuan" class="form-label">Satuan</label>
                <select name="satuan" id="satuan" class="form-select satuan"></select>

                <span class="d-block text-danger" id="error_satuan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="bidang_permintaan" class="form-label">Bidang Permintaan</label>
                <select name="bidang_permintaan" id="bidang_permintaan" class="form-select bidang-permintaan">
                    @foreach ($dataConfigStockField as $config)
                    <option value="{{ $config->id }}">{{ $config->name }}</option>
                    @endforeach
                </select>

                <span class="d-block text-danger" id="error_bidang_permintaan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="nama_asset" class="form-label">Nama Asset Logistik</label>
                <input type="text" name="nama_asset" id="nama_asset" class="form-control">

                <span class="d-block text-danger" id="error_nama_asset"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="kode_asset" class="form-label">Kode Asset Logistik</label>
                <input type="text" name="kode_asset" id="kode_asset" class="form-control" readonly>

                <span class="d-block text-danger" id="error_kode_asset"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="kode_register" class="form-label">Kode Register Logistik <i class="text-muted">*(Max 3 digit)</i> </label>
                <input type="number" name="kode_register" id="kode_register" class="form-control">

                <span class="d-block text-danger" id="error_kode_register"></span>
            </div>

            <div class="form-group col-md-12 mb-3">
                <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</button>
                <button type="button" class="btn btn-info" id="btn-save-add"><i class="fas fa-save me-1"></i>Simpan & Tambah</button>
                <a href="{{ route('logistic.asset.index') }}" class="btn btn-outline-danger"><i class="fas fa-undo me-1"></i>Kembali</a>
            </div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
<script src="{{ asset('/js/app/logistic/createAsset.js') }}"></script>
@endpush