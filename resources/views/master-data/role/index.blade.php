@extends('layouts.app')

@push('style')
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
    rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push('menu')
@include('menu.master_data')
@endpush

@section('content')
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
    </div>
    <div class="panel-body">


        <div class="table-responsive">
            <table class="table table-bordered table-striped w-100" id="datatable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Kode Role</th>
                        <th>Nama Role</th>
                        <th>Informasi</th>
                        <th>Action</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Role</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <form action="" method="post" id="formdata">
                    <div class="form-group mb-3">
                        <label class="form-label">Kode Role</label>
                        <input type="text" class="form-control" name="kode_role" id="kode_role">

                        <div class="d-block text-danger error-message" id="error_kode_role"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Nama Role</label>
                        <input type="text" class="form-control" name="nama_role" id="nama_role">

                        <div class="d-block text-danger error-message" id="error_nama_role"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Deskripsi</label>
                        <input type="text" class="form-control" name="deskripsi" id="deskripsi">

                        <div class="d-block text-danger error-message" id="error_deskripsi"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Permission</label>
                        <select name="permissions[]" id="permission" class="form-select permission" multiple>

                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Batal</a>
                <a href="javascript:;" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/master/app.js') }}"></script>
<script src="{{ asset('/js/app/master/role.js') }}"></script>
@endpush