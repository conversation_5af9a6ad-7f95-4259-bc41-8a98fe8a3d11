@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.logistic")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex justify-content-end">
            <button id="btn_export_excel" data-export-url="{{ route('logistic.kartu-stock.export') }}" data-export-filename="Kartu Stock"
                data-params="{'start_date': '', 'end_date': ''}"
                class="btn btn-secondary btn-sm d-flex me-2 pe-3 rounded-3" disabled>
                <i class="fa fa-file-excel"></i> &nbsp; Export Excel
            </button>
        </div>
        <div class="row d-flex mb-3">
            <div class="form-group col-md-4">
                <label for="daterange" class="form-label"><i>Periode</i></label>
                <input type="text" id="datepicker" name="daterange" class="form-control">
            </div>

            <div class="form-group col-md-4">
                <label for="asset" class="form-label"><i>Asset</i></label>
                <select name="asset" id="asset" class="form-control asset"></select>
            </div>

            <div class="form-group col-md-4">
                <button type="button" class="btn btn-primary mt-20px" id="btn-filter"><i class="fas fa-filter me-1"></i>Tampilkan</button>
            </div>
        </div>

        <div id="target-report">

        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/moment/moment.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/logistic/kartuStock.js') }}"></script>
@endpush