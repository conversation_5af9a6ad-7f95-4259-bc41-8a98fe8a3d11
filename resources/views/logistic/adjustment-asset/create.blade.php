@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
@endpush

@push('menu')
    @include('menu.logistic')
@endpush

@section('content')
    <style>
        .select2-results__option {
            font-size: 80%;
        }

        .select2-selection__rendered {
            font-size: 80%;
        }
    </style>

    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
        </div>
        <form action="" method="post" id="form-activity">
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-3">
                        Tipe Penyesuaian
                        <select name="adjustment_type" id="adjustment_type" class="form-select distributor w-100">
                            <option value="">Pilih <PERSON></option>
                            <option value="ADJUSTMENT_EXCESS"><PERSON><PERSON><PERSON><PERSON></option>
                            <option value="ADJUSTMENT_SHORTAGE"><PERSON><PERSON><PERSON><PERSON></option>
                        </select>
                    </div>
                    <div class="col-sm-6">
                    </div>
                    <div class="col-sm-3">
                        <div class="mb-6">
                            <div class="text-sm-left">
                                Tanggal Penyesuaian
                                <input type="date" name="logistic_date" class="form-control" value="{{ old('tanggal_pembayaran') ?? now()->format('Y-m-d') }}">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3 mt-5">
                    <button type="button" id="btn-add-row" class="btn btn-outline-primary me-1"><i class="fas fa-plus-circle me-1"></i>Tambah Baris</button>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped w-100 mb-3">
                        <thead>
                        <tr>
                            <th style="max-width: 300px !important;">Barang Habis Pakai</th>
                            <th>Detail Barang</th>
                            <th>Qty</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody id="table-allocation"></tbody>
                    </table>
                </div>

                <div class="row my-4">
                    <div class="col-md-3">
                        Catatan:
                        <textarea style="width:100%;;" rows="3" cols="3" name="notes" class="form-control"
                                  placeholder="Catatan / Deskripsi"></textarea>
                        <br>
                        <span style="cursor: pointer" id="file_attach"><i class="fa fa-file-pdf"></i> File Lampiran
                            <i>(*jika dibutuhkan)</i></span>
                        <input type="file" name="document" id="attachment_link" class="form-control">
                    </div>
                    <div class="col-md-9">
                    </div>
                </div>

                <div class="form-group d-flex justify-content-end">
                    <button type="button" id="btn-save" class="btn btn-primary"><i class="fas fa-save me-1"></i>Simpan
                        Form
                    </button>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/logistic/createAdjustmentAsset.js') }}"></script>
@endpush
