@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
@if ($errors->any())
<div class="alert alert-danger alert-dismissible fade show">
    <strong>Error!</strong> <br>
    @foreach ($errors->all() as $error)
    <li>{{ $error }}</li>
    @endforeach
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex justify-content-end mb-3">
            <x-asset-document.helper_book></x-asset-document.helper_book>
            <x-asset-document.download></x-asset-document.download>
            <x-asset-document.upload></x-asset-document.upload>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-striped w-100" id="datatable">
                <thead>
                    <tr>
                        <th rowspan="2" class="align-middle">#</th>
                        <th rowspan="2" class="align-middle">Foto</th>
                        <th rowspan="2" class="align-middle">Kode QR</th>
                        <th rowspan="2" class="align-middle">Nama Barang</th>
                        <th rowspan="2" class="align-middle">Kode Barang</th>
                        <th rowspan="2" class="align-middle">Kode Register / SN</th>
                        <th rowspan="2" class="align-middle">Peruntukan</th>
                        <th colspan="5" class="text-center">Dokumen BAST</th>
                    </tr>
                    <tr>
                        <th>Ceklis</th>
                        <th>Penempatan</th>
                        <th>Pembayaran</th>
                        <th>Mutasi</th>
                        <th>Aset Rusak</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-document-preview">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Preview Dokumen</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body" id="list-doc">
                <!-- <div class="row mb-3">
                    <iframe src="" frameborder="0" height="500px" width="100%" id="frame-doc"></iframe>
                </div> -->

                <!-- <div class="row" id="row-image">
                </div> -->
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-img-preview">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <img src="" class="img-asset-preview" alt="">
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/asset-management/assetDocument.js') }}"></script>
@endpush