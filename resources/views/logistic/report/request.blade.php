@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
        rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
@endpush

@push('menu')
    @include('menu.logistic')
@endpush

@section('content')
    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
        </div>
        <div class="panel-body">
            <div class="d-flex justify-content-end">
                <button id="btn_export_excel" data-export-url="{{ $exportRoute }}" data-export-filename="{{ $title }}"
                    data-params="{'start_date': '', 'end_date': '', 'room_id': ''}"
                    class="btn btn-secondary btn-sm d-flex me-2 pe-3 rounded-3" disabled>
                    <i class="fa fa-file-excel"></i> &nbsp; Export Excel
                </button>
            </div>
            <div class="d-flex row mb-5">
                <div class="form-group col-md-4">
                    <label class="form-label"><i>Periode</i></label>
                    <input id="datepicker" type="text" class="form-control" readonly="readonly"
                        style="background-color:white; cursor:pointer;">
                </div>

                <div class="form-group col-md-4">
                    <label class="form-label"><i>Ruangan</i></label>
                    <select style="width: 100%" name="target_ruangan" id="target_ruangan" class="form-select target_ruangan"></select>
                </div>

                <div class="form-group col-md-4">
                    <button type="button" id="btn-filter" class="btn btn-primary mt-20px">Tampilkan Data</button>
                </div>
            </div>

            <div class="target-report">
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/moment/moment.js"></script>
    <script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="{{ asset('js/app/logistic/report.js') }}"></script>
@endpush
