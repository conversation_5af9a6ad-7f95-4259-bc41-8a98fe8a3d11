@extends("layouts.app")

@push("style")
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
    @include("menu.depreciation")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex row mb-5">
            <div class="form-group col-md-3">
                <label class="form-label"><i>Ruangan</i></label>
                <select name="ruangan" id="ruangan" class="form-select room"></select>
            </div>

            <div class="form-group col-md-3">
                <label class="form-label"><i>Barang</i></label>
                <select name="item_id" id="item_id" class="form-select item_id"></select>
            </div>

            <div class="form-group col-md-3">
                <label class="form-label"><i>Tahun Perolehan</i></label>
                <select id="yearpicker" name="yearpicker" class="form-select">
                    <option value="">Semua Tahun</option>
                    @php
                        $currentYear = date('Y');
                        $startYear = $currentYear - 10;
                        $endYear = $currentYear + 1;
                    @endphp
                    @for ($year = $startYear; $year <= $endYear; $year++)
                        <option value="{{ $year }}">{{ $year }}</option>
                    @endfor
                </select>
            </div>

            <div class="form-group col-md-3">
                <button type="button" id="btn-filter" class="btn btn-primary mt-20px">Tampilkan Data</button>
            </div>
        </div>

        <div class="target-report">
        </div>
    </div>
</div>
@endsection

@push("script")
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/depreciation/report.js') }}"></script>
@endpush
