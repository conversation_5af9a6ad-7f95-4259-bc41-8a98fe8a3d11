@extends('layouts.app')

@push('menu')
    @include('menu.asset_maintenance')
@endpush

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
        rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
@endpush

@section('content')
    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
        </div>
        <div class="panel-body">
            <div class="">
                <div class="d-lg-flex mb-lg-3 mb-2">
                    <h1 class="page-header mb-0 flex-1"></h1>
                    <div class="d-lg-flex align-items-center">
                        <div class="btn-group btn-secondary rounded-3 mx-1">
                            <a href="#" class="btn btn-sm btn-secondary"><i class="fa fa-filter"></i></a>
                            <a href="#" class="btn btn-sm btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fa fa-caret-down"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <a onclick="changeReportType('period')" href="javascript:void(0)"
                                    class="dropdown-item">Pencarian Periode</a>
                                <a onclick="changeReportType('qr')" href="javascript:void(0)"
                                    class="dropdown-item">Pencarian QR</a>
                            </ul>
                        </div>

                        {{-- <a href="#" class="btn btn-secondary btn-sm mx-1">
                            <i class="fa fa-print"></i> &nbsp; Print Result
                        </a> --}}
                        <button id="btn_export_excel" data-export-url="export-detail"
                            data-export-filename="Pemeliharaan Detail"
                            data-params="{'start_date': '', 'end_date': '', 'maintenance_category': '', 'maintenance_type': ''}"
                            class="btn btn-secondary btn-sm d-flex me-2 pe-3 rounded-3" disabled>
                            <i class="fa fa-file-excel"></i> &nbsp; Export Excel
                        </button>

                    </div>
                </div>
                <div class="row gx-2 pb-10px">

                    <div class="col-lg-4 report_qr" style="display: none">
                        <label class="form-label"><i>Asset</i></label>
                        <select name="asset_id" id="asset_id" class="form-select w-100"></select>
                    </div>

                    <div class="col-lg-2 report_category">
                        <label class="form-label"><i>Kategori</i></label>
                        <select name="maintenance_category" id="maintenance_category" class="form-select asal-perolehan">
                            <option value="ALKES">Alat Kesehatan</option>
                            <option value="NON_ALKES">Non Alat Kesehatan</option>
                        </select>
                    </div>
                    <div class="col-lg-2 report_category">
                        <label class="form-label"><i>Tipe</i></label>
                        <select name="maintenance_type" id="maintenance_type" class="form-select asal-perolehan">
                            <option value="" selected>Semua Kategori</option>
                            <option value="KALIBRASI">Kalibrasi</option>
                            <option value="MAINTENANCE">Maintenance</option>
                            <option value="UJI_FUNGSI">Uji Fungsi</option>
                            <option value="UJI_KESESUAIAN">Uji Kesesuaian</option>
                        </select>
                    </div>
                    <div class="col-lg-3 report_category">
                        <label class="form-label"><i>Periode</i></label>
                        <input id="datepicker" type="text" class="form-control" readonly="readonly"
                            style="background-color:white; cursor:pointer;">
                    </div>
                    <div class="col-lg-2">
                        <button style="margin-top: 22px;" type="button" id="btn_show_report"
                            class="btn btn-primary">Tampilkan Data</button>
                    </div>

                </div>
            </div>
            <hr>
            <div class="mt-3" id="report_page"></div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/moment/moment.js"></script>
    <script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="{{ asset('/js/app/asset-maintenance/app.js') }}"></script>
    <script src="{{ asset('/js/app/asset-maintenance/report.js') }}"></script>
@endpush
