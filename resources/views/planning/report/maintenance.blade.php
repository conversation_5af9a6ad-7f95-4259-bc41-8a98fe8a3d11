@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.planning")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        @php
            $currentYear = date('Y');
            $minYear = $currentYear - 10;
            $maxYear = $currentYear + 2;
        @endphp
        
        <div class="d-flex align-items-center gap-2 mb-3">
            <div class="form-group" style="min-width: 200px;">
                <select id="year" name="year" class="form-control">
                    <option value="">Pi<PERSON>h <PERSON></option>
                    @for($year = $maxYear; $year >= $minYear; $year--)
                        <option value="{{ $year }}" {{ $year == $currentYear ? 'selected' : '' }}>{{ $year }}</option>
                    @endfor
                </select>
            </div>
            <button type="button" class="btn btn-primary" id="btn-filter">
                <i class="fas fa-filter me-1"></i>Tampilkan
            </button>
            <button type="button" class="btn btn-success" id="btn-export">
                <i class="fas fa-file-excel me-1"></i>Export Excel
            </button>
        </div>

        <div id="target-report">
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/moment/moment.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/planning/maintenance_plan_report.js') }}"></script>
@endpush