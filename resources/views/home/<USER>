@extends("layouts.home")

@section("content")
<div class="main-content">
    <div class="container">
        <!-- Header Section -->
        <div class="text-center mb-2">
            <h3 class="main-title">Sensi</h3>
            <p class="lead">Aplikasi Management Asset & Logistik Rumah <PERSON>kit</p>
        </div>

        <!-- Main Menu Cards -->
        <div class="row justify-content-center pt-5">
            @if(hasPermissionInGuard('Master Data'))
            <a href="{{ route('master-data.home.index') }}" class="col-lg-4 col-md-6 mb-4 text-decoration-none">
                <div class="module-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-cogs"></i>
                        </div>
                        <div class="card-title">
                            <h3>Master Data</h3>
                        </div>
                    </div>
                    <div class="card-content">
                        <p>Setup master data dan konfigurasi sistem untuk mengelola pengaturan dasar aplikasi</p>
                    </div>
                </div>
            </a>
            @endif

            @if(hasPermissionInGuard('Manajemen Aset'))
            <a href="{{ route('asset-management.home.index') }}" class="col-lg-4 col-md-6 mb-4 text-decoration-none">
                <div class="module-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-cubes"></i>
                        </div>
                        <div class="card-title">
                            <h3>Manajemen Aset</h3>
                        </div>
                    </div>
                    <div class="card-content">
                        <p>Kelola dan monitor semua aset rumah sakit dengan sistem tracking yang terintegrasi</p>
                    </div>
                </div>
            </a>
            @endif

            @if(hasPermissionInGuard('Pemeliharaan Aset'))
            <a href="{{ route('maintenance-asset.home.index') }}" class="col-lg-4 col-md-6 mb-4 text-decoration-none">
                <div class="module-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-wrench"></i>
                        </div>
                        <div class="card-title">
                            <h3>Pemeliharaan Aset</h3>
                        </div>
                    </div>
                    <div class="card-content">
                        <p>Jadwalkan dan lakukan pemeliharaan rutin untuk memastikan aset tetap optimal</p>
                    </div>
                </div>
            </a>
            @endif

            @if(hasPermissionInGuard('Logistik'))
            <a href="{{ route('logistic.home.index') }}" class="col-lg-4 col-md-6 mb-4 text-decoration-none">
                <div class="module-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-truck"></i>
                        </div>
                        <div class="card-title">
                            <h3>Logistik</h3>
                        </div>
                    </div>
                    <div class="card-content">
                        <p>Kelola distribusi dan pergerakan aset antar unit dengan tracking real-time</p>
                    </div>
                </div>
            </a>
            @endif

            @if(hasPermissionInGuard('Penyusutan'))
            <a href="{{ route('depreciation.home.index') }}" class="col-lg-4 col-md-6 mb-4 text-decoration-none">
                <div class="module-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-chart-line"></i>
                        </div>
                        <div class="card-title">
                            <h3>Penyusutan</h3>
                        </div>
                    </div>
                    <div class="card-content">
                        <p>Hitung dan monitor penyusutan aset secara otomatis</p>
                    </div>
                </div>
            </a>
            @endif

            @if(hasPermissionInGuard('Perencanaan'))
            <a href="{{ route('planning.home.index') }}" class="col-lg-4 col-md-6 mb-4 text-decoration-none">
                <div class="module-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-calendar-check"></i>
                        </div>
                        <div class="card-title">
                            <h3>Perencanaan</h3>
                        </div>
                    </div>
                    <div class="card-content">
                        <p>Rencanakan pengadaan dan pengembangan aset berdasarkan analisis kebutuhan</p>
                    </div>
                </div>
            </a>
            @endif
        </div>
    </div>
</div>

<!-- Footer Section -->
<footer class="footer">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <h5>Tentang Sistem</h5>
                <p>
                    Platform pengelolaan data aset secara komprehensif dari pencatatan, pendokumentasian, 
                    penempatan dan mutasi, pemantauan pemeliharaan, penghapusan aset, hingga pelaporan.
                </p>
            </div>
            <div class="col-md-4">
                <h5>{{ config('app.hospital_name') }}</h5>
                <p>
                    <i class="fas fa-map-marker-alt me-2"></i>
                    {{ config('app.hospital_address') }}
                </p>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-12 text-center">
                <p class="mb-0">
                    © {{ date('Y') }} Sensi. All rights reserved.
                </p>
            </div>
        </div>
    </div>
</footer>

<style>
    html, body {
        height: 100%;
        background: #ffffff;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        color: #1a1a1a;
        line-height: 1.6;
    }

    body {
        display: flex;
        flex-direction: column;
    }

    .main-content {
        flex: 1 0 auto;
        padding: 2rem 0;
        background: #f4f4f4;
    }

    .app-content {
        background: #f4f4f4;
    }

    .main-title {
        font-size: 3.5rem;
        font-weight: 800;
        color: #1a1a1a;
        margin-bottom: 0.5rem;
        letter-spacing: -0.02em;
    }

    .sub-title {
        font-size: 1.25rem;
        font-weight: 500;
        color: #6b7280;
        margin-bottom: 1rem;
        letter-spacing: 0.01em;
    }

    .lead {
        font-size: 1.125rem;
        color: #6b7280;
        font-weight: 400;
        max-width: 600px;
        margin: 0 auto;
    }

    .module-card {
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 16px;
        padding: 2rem;
        height: 100%;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .module-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .module-card:hover {
        border-color: #3b82f6;
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
    }

    .module-card:hover::before {
        opacity: 1;
    }

    .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        background-color: #fff;
    }

    .card-icon {
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.5rem;
        flex-shrink: 0;
    }

    .card-icon i {
        font-size: 1.2rem;
        color: white;
    }

    .card-title h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0;
        letter-spacing: -0.01em;
    }

    .card-content p {
        color: #6b7280;
        font-size: 0.95rem;
        margin: 0;
        line-height: 1.5;
    }

    .footer {
        flex-shrink: 0;
        padding: 3rem 0 2rem;
        background: #f9fafb;
        border-top: 1px solid #e5e7eb;
    }

    .footer h5 {
        color: #1a1a1a;
        margin-bottom: 1rem;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .footer p {
        color: #6b7280;
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .footer hr {
        border-color: #e5e7eb;
        margin: 2rem 0 1rem;
    }

    @media (max-width: 768px) {
        .main-content {
            padding: 2rem 0;
        }
        
        .main-title {
            font-size: 2.5rem;
        }
        
        .sub-title {
            font-size: 1.1rem;
        }
        
        .lead {
            font-size: 1rem;
        }
        
        .module-card {
            padding: 1.5rem;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
        }
        
        .card-icon i {
            font-size: 1.25rem;
        }
    }

    @media (max-width: 576px) {
        .main-title {
            font-size: 2rem;
        }
        
        .module-card {
            padding: 1.25rem;
        }
    }
</style>
@endsection
